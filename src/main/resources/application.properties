spring.application.name=quizApp
spring.datasource.url=*************************************************************************************
spring.datasource.username=neondb_owner
spring.datasource.password=npg_RXw0HgCZ1Kzm
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=kecsvoultcdwllop 
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

jwt.secret=qwrtyhbvcxzsertyuiolmnbvcxsrtyuioknbvcxsertyu

app.frontend.url=https://spring-boot-quiz-app.onrender.com